package com.ets.delivery.application.app.thirdservice.feign;

import com.ets.delivery.application.app.thirdservice.fallback.PhpIssuerAdminFallbackFactory;
import com.ets.delivery.application.app.thirdservice.request.ReviewRiskResultNotifyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        url = "${microUrls.issuer-admin:http://etc-micro-issuer-admin:7001}",
        name = "PhpIssuerAdminFeign",
        fallbackFactory = PhpIssuerAdminFallbackFactory.class
)
public interface PhpIssuerAdminFeign {

    String reviewRiskResultNotify(@RequestBody ReviewRiskResultNotifyDTO notifyDTO);
}
