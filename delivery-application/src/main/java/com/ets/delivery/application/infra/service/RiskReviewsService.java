package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewListDTO;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviews;
import com.ets.delivery.application.infra.mapper.RiskReviewsMapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 风控审核单记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class RiskReviewsService extends BaseService<RiskReviewsMapper, RiskReviews> {

    public IPage<RiskReviews> getList(RiskReviewListDTO listDTO) {

        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(listDTO.getRiskType() != null, RiskReviews::getRiskType, listDTO.getRiskType())
                .eq(listDTO.getIssuerId() != null, RiskReviews::getIssuerId, listDTO.getIssuerId())
                .eq(listDTO.getUid() != null, RiskReviews::getUid, listDTO.getUid())
                .eq(listDTO.getPlateNo() != null, RiskReviews::getPlateNo, listDTO.getPlateNo())
                .eq(listDTO.getBusinessSn() != null, RiskReviews::getBusinessSn, listDTO.getBusinessSn())
                .eq(listDTO.getRiskReviewSn() != null, RiskReviews::getRiskReviewSn, listDTO.getRiskReviewSn())
                .eq(listDTO.getRiskReviewStatus() != null, RiskReviews::getRiskReviewStatus, listDTO.getRiskReviewStatus())
                .eq(listDTO.getRejectReasonId() != null, RiskReviews::getRejectReasonId, listDTO.getRejectReasonId())
                .eq(listDTO.getAutoAudit() != null, RiskReviews::getAutoAudit, listDTO.getAutoAudit())
                .ge(ObjectUtils.isNotEmpty(listDTO.getCreateStartTime()), RiskReviews::getCreatedAt, listDTO.getCreateStartTime())
                .le(ObjectUtils.isNotEmpty(listDTO.getCreateEndTime()), RiskReviews::getCreatedAt, listDTO.getCreateEndTime())
                .like(StringUtils.isNotEmpty(listDTO.getRiskRuleId()), RiskReviews::getRiskRuleIds, "\"" + listDTO.getRiskRuleId() + "\"");

        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }

    /**
     * 根据业务单号查询风控审核单
     *
     * @param businessSn 业务单号
     * @return 风控审核单
     */
    public RiskReviews getByBusinessSn(String businessSn) {
        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getBusinessSn, businessSn)
                .orderByDesc(RiskReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据风控单号查询风控审核单
     *
     * @param riskReviewSn 风控单号
     * @return 风控审核单
     */
    public RiskReviews getByRiskReviewSn(String riskReviewSn) {
        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getRiskReviewSn, riskReviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据业务单号和风控类型查询风控审核单
     *
     * @param businessSn 业务单号
     * @param riskType 风控类型
     * @return 风控审核单
     */
    public RiskReviews getByBusinessSnAndRiskType(String businessSn, Integer riskType) {
        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getBusinessSn, businessSn)
                .eq(RiskReviews::getRiskType, riskType)
                .orderByDesc(RiskReviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 获取待审核的风控审核单列表（用于队列重新入队）
     *
     * @param riskType 风控类型
     * @param daysRange 天数范围，默认7天内的记录
     * @param limit 限制数量
     * @return 待审核的风控审核单列表
     */
    public List<RiskReviews> getDefaultListLimitDate(Integer riskType, Integer daysRange, Integer limit) {
        if (daysRange == null) {
            daysRange = 7;
        }
        if (limit == null) {
            limit = 50;
        }

        Wrapper<RiskReviews> wrapper = Wrappers.<RiskReviews>lambdaQuery()
                .eq(RiskReviews::getRiskReviewStatus, RiskReviewStatusEnum.PENDING.getValue())
                .eq(riskType != null, RiskReviews::getRiskType, riskType)
                .gt(RiskReviews::getCreatedAt, LocalDateTime.now().minusDays(daysRange))
                .orderByAsc(RiskReviews::getCreatedAt)
                .last("limit " + limit);
        return this.baseMapper.selectList(wrapper);
    }
}
