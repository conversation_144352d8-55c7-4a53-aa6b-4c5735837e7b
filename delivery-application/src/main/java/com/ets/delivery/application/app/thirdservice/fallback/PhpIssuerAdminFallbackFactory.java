package com.ets.delivery.application.app.thirdservice.fallback;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.thirdservice.feign.PhpIssuerAdminFeign;
import com.ets.delivery.application.app.thirdservice.request.ReviewRiskResultNotifyDTO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import static com.ets.common.JsonResult.error;

@Component
public class PhpIssuerAdminFallbackFactory implements FallbackFactory<PhpIssuerAdminFeign> {

    @Override
    public PhpIssuerAdminFeign create(Throwable cause) {
        return new PhpIssuerAdminFeign() {
            @Override
            public String reviewRiskResultNotify(ReviewRiskResultNotifyDTO notifyDTO) {
                return JsonResult.error("调用issuer-admin服务失败：" + cause.getMessage()).toString();
            }
        };
    }
}
