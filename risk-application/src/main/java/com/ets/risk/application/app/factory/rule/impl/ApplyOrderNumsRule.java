package com.ets.risk.application.app.factory.rule.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ets.common.JsonResult;
import com.ets.risk.application.app.thirdservice.feign.RiskApplyFeign;
import com.ets.risk.application.app.thirdservice.request.GetOrderRiskCheckDTO;
import com.ets.risk.application.app.thirdservice.response.GetOrderRiskCheckVO;
import com.ets.risk.application.common.bo.rule.RuleResultBo;
import com.ets.risk.application.common.consts.rule.RiskRuleItemStatusEnum;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskRuleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public  class ApplyOrderNumsRule extends RuleBase {
    @Autowired
    private RiskApplyFeign applyFeign;
    @Override
    public RuleResultBo getResult(RiskEntity risk, RiskRuleEntity riskRule) {
        RuleResultBo ruleResultBo = new RuleResultBo();
        //默认不命中规则
        ruleResultBo.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_REJECT.getStatus());
        //调用业务方接口获取数量
        JsonResult<GetOrderRiskCheckVO> result = applyFeign.getOrderRiskCheckInfo(new GetOrderRiskCheckDTO(risk.getBusinessSn(),riskRule.getRuleParams()));
        log.info(result.getDataWithCheckError().toString());
        int queryNums = result.getDataWithCheckError().getTotal();
        //请求结果记录
        ruleResultBo.setRuleResult(JSON.toJSONString(result.getDataWithCheckError()));
        //忽略结果的条件
        if(riskRule.getIgnoreCondition() != null){
            JSONObject riskRuleIgnoreCondition = JSON.parseObject(riskRule.getIgnoreCondition());
            JSONArray ignoreCondition = riskRuleIgnoreCondition.getJSONArray("value");
            String matchValue = result.getDataWithCheckError().getRuleData().get(riskRule.getRuleParams());
            if (ignoreCondition.contains(matchValue)) {
                ruleResultBo.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_REJECT.getStatus());
                return ruleResultBo;
            }
        }

        //命中结果的条件
        JSONObject riskRuleCondition = JSON.parseObject(riskRule.getRuleCondition());
        String exp = riskRuleCondition.getString("exp");
        Integer conditionNums = riskRuleCondition.getInteger("value");
        switch (exp){
            case "gt":
                if(queryNums > conditionNums){
                    ruleResultBo.setItemStatus(RiskRuleItemStatusEnum.TASK_STATUS_PASS.getStatus());
                }
                break;
            default:
                break;
        }
        return ruleResultBo;
    }
}
