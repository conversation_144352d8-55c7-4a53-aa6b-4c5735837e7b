package com.ets.risk.application.app.business;


import com.ets.common.ToolsHelper;
import com.ets.risk.application.app.factory.task.TaskFactory;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.risk.application.common.dto.risk.RiskAcceptDTO;
import com.ets.risk.application.common.dto.task.TaskRecordDTO;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.service.RiskRuleService;
import com.ets.risk.application.infra.service.RiskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RiskBusiness {
    @Autowired
    private RiskService riskService;
    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private RiskRuleService riskRuleService;
    /*
     *  接收要风控数据
     */
    public Boolean accept(RiskAcceptDTO dto) {
        //获取风控规则
        List<Integer> ruleIds = riskRuleService.getRiskRuleIds(dto.getBusinessType());
        RiskEntity risk = new RiskEntity();
        String riskSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskAccept", "prod", 8);
        risk.setRiskSn(riskSn);
        risk.setBusinessSn(dto.getBusinessSn());
        risk.setBusinessType(dto.getBusinessType());
        risk.setRiskParams(dto.getRiskParams());
        risk.setNotifyUrl(dto.getNotifyUrl());
        risk.setRiskRuleIds(ruleIds.toString());
        riskService.create(risk);

        //塞队列进行发货操作
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(riskSn);
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_RISK_DEAL.getType());
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_RISK_DEAL).addAndPush(taskRecordDTO);
        return true;
    }
}
