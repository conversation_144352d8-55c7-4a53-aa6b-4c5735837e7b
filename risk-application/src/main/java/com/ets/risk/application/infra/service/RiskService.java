package com.ets.risk.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskItemEntity;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.mapper.RiskMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 风控记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
@DS("db-issuer-admin")
public class RiskService extends BaseService<RiskMapper, RiskEntity> {
    /*
     *   更新risk的状态及原因
     */
    public void updateRiskStatus(String riskSn,Integer riskStatus,String riskRemark){
        LambdaUpdateWrapper<RiskEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskEntity::getRiskSn, riskSn)
                           .set(RiskEntity::getRiskStatus, riskStatus)
                           .set(RiskEntity::getRiskRemark, riskRemark)
                           .set(RiskEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }
    /*
     *  通过业务单号+状态 获取risk的item数量
     */
    public Long getCountByBusinessSn(String businessSn,Integer riskStatus){
        Wrapper<RiskEntity> wrapper = Wrappers.<RiskEntity>lambdaQuery()
                .eq(RiskEntity::getBusinessSn, businessSn)
                .eq(RiskEntity::getRiskStatus, riskStatus);
        return super.baseMapper.selectCount(wrapper);
    }
    /*
     *   更新risk的通知状态
     */
    public void updateNotifyStatus(String riskSn,Integer notifyStatus){
        LambdaUpdateWrapper<RiskEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskEntity::getRiskSn, riskSn)
                .set(RiskEntity::getNotifyStatus, notifyStatus)
                .set(RiskEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }
}
