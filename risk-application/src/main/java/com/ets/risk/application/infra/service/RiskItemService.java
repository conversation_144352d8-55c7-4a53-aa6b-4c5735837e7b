package com.ets.risk.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.risk.application.common.consts.rule.RiskRuleItemStatusEnum;
import com.ets.risk.application.infra.entity.RiskEntity;
import com.ets.risk.application.infra.entity.RiskItemEntity;
import com.ets.risk.application.infra.mapper.RiskItemMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 风控规则匹配明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */

@Service
@DS("db-issuer-admin")
public class RiskItemService extends BaseService<RiskItemMapper, RiskItemEntity> {
    public List<RiskItemEntity> getListByRiskSn(String riskSn,Integer itemStatus){
        Wrapper<RiskItemEntity> wrapper = Wrappers.<RiskItemEntity>lambdaQuery()
                .eq(RiskItemEntity::getRiskSn, riskSn)
                .eq(RiskItemEntity::getItemStatus, itemStatus);
        return super.baseMapper.selectList(wrapper);
    }

    /*
     *   更新risk的状态及原因
     */
    public void updateRiskStatus(String itemSn,Integer riskStatus,String ruleResult){
        LambdaUpdateWrapper<RiskItemEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskItemEntity::getItemSn, itemSn)
                .set(RiskItemEntity::getItemStatus, riskStatus)
                .set(RiskItemEntity::getRuleResult, ruleResult)
                .set(RiskItemEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }

    public Long getCountByRiskSn(String riskSn,Integer itemStatus){
        Wrapper<RiskItemEntity> wrapper = Wrappers.<RiskItemEntity>lambdaQuery()
                .eq(RiskItemEntity::getRiskSn, riskSn)
                .eq(RiskItemEntity::getItemStatus, itemStatus);
        return super.baseMapper.selectCount(wrapper);
    }


    /*
     *   更新risk的状态及原因
     */
    public void cancelByRiskSn(String riskSn,String remark){
        LambdaUpdateWrapper<RiskItemEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(RiskItemEntity::getRiskSn, riskSn)
                .set(RiskItemEntity::getItemStatus, RiskRuleItemStatusEnum.TASK_STATUS_CANCEL.getStatus())
                .set(RiskItemEntity::getRemark, remark)
                .set(RiskItemEntity::getUpdatedAt, LocalDateTime.now())
        ;
        updateByWrapper(lambdaUpdateWrapper);
    }
}
