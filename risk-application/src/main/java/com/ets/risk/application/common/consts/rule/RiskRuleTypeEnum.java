package com.ets.risk.application.common.consts.rule;

import com.ets.risk.application.app.factory.rule.impl.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RiskRuleTypeEnum {

    RISK_RULE_ONE_MAIN_SWITCH("mainSwitch", MainSwitchRule.class, "主开关"),

    RISK_RULE_SECOND_COMMON_MATCH_INT("commonMatchInt", CommonMatchIntRule.class, "通过匹配int类型"),

    RISK_RULE_THIRD_APPLY_ORDER_NUMS("applyOrderNums", ApplyOrderNumsRule.class, "申办订单数量"),
    RISK_RULE_CHECK_HIT_COUNTS("checkHitCounts", CheckHitCountsRule.class, "检查是否命中过"),

    ;

    private final String ruleFunc;
    private final Class<? extends RuleBase> ruleFuncClass;
    private final String desc;

    public static RiskRuleTypeEnum getByType(String ruleFunc) {

        for (RiskRuleTypeEnum node : RiskRuleTypeEnum.values()) {
            if (node.getRuleFunc().equals(ruleFunc)) {
                return node;
            }
        }
        return null;
    }


}
