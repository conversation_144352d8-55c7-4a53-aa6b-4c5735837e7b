package com.ets.risk.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.risk.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.risk.application.common.dto.task.TaskRecordDTO;
import com.ets.risk.application.infra.entity.RiskTaskRecord;
import com.ets.risk.application.infra.mapper.RiskTaskRecordMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 任务记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("db-issuer-admin")
@Repository(value = "RiskTaskRecordService")
public class RiskTaskRecordService extends BaseService<RiskTaskRecordMapper, RiskTaskRecord> {

    public RiskTaskRecord getOneByCondition(String referSn, String referType, String notifyContent) {
        Wrapper<RiskTaskRecord> wrapper = Wrappers.<RiskTaskRecord>lambdaQuery()
                .eq(RiskTaskRecord::getReferSn, referSn)
                .eq(RiskTaskRecord::getReferType, referType)
                .eq(ObjectUtils.isNotEmpty(notifyContent), RiskTaskRecord::getNotifyContent, notifyContent)
                .last("limit 1")
                .orderByDesc(RiskTaskRecord::getCreatedAt);
        return this.baseMapper.selectOne(wrapper);
    }

    /*
     * 通过taskSn获取
     */
    public RiskTaskRecord getOneByTaskSn(String taskSn) {
        Wrapper<RiskTaskRecord> wrapper = Wrappers.<RiskTaskRecord>lambdaQuery()
            .eq(RiskTaskRecord::getTaskSn, taskSn)
            .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /*
     * 创建task
     */
    public RiskTaskRecord addNew(TaskRecordDTO taskRecordDTO) {

        RiskTaskRecord riskTaskRecord = new RiskTaskRecord();
        riskTaskRecord.setTaskSn(taskRecordDTO.getTaskSn());
        riskTaskRecord.setReferSn(taskRecordDTO.getReferSn());
        riskTaskRecord.setReferType(taskRecordDTO.getReferType());
        riskTaskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        riskTaskRecord.setNextExecTime(taskRecordDTO.getNextExecTime());
        riskTaskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        riskTaskRecord.setStatus(taskRecordDTO.getStatus());
        riskTaskRecord.setCreatedAt(LocalDateTime.now());
        riskTaskRecord.setUpdatedAt(LocalDateTime.now());
        this.save(riskTaskRecord);

        return riskTaskRecord;
    }

    /*
     * 获取创建时间之前的数据
     */
    public List<RiskTaskRecord> getListByCreatedAt(Integer minusDays) {
        LambdaQueryWrapper<RiskTaskRecord> wrapper = new QueryWrapper<RiskTaskRecord>().lambda()
                .in(RiskTaskRecord::getStatus, Arrays.asList(
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                ))
                .in(RiskTaskRecord::getReferType, TaskRecordReferTypeEnum.getTypeList())
                .gt(RiskTaskRecord::getCreatedAt, LocalDateTime.now().minusDays(minusDays))
                .orderByAsc(RiskTaskRecord::getCreatedAt)
                .last("limit 1000");

        return super.baseMapper.selectList(wrapper);
    }

    public RiskTaskRecord getByReferSn(String referSn, String referType) {

        LambdaQueryWrapper<RiskTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RiskTaskRecord::getReferSn, referSn)
                .eq(RiskTaskRecord::getReferType, referType);

        return getOneByWrapper(wrapper);
    }
}
